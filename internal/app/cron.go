package app

import (
	"fmt"
	"time"

	"github.com/google/uuid"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/model"
	"gorm.io/gorm"
)

func StartReferralSnapshotCron(db *gorm.DB) {
	ticker := time.NewTicker(10 * time.Second)
	go func() {
		for {
			<-ticker.C
			updateAllReferralSnapshots(db)
		}
	}()
}

func updateAllReferralSnapshots(db *gorm.DB) {
	var userIDs []uuid.UUID
	db.Model(&model.User{}).Pluck("id", &userIDs)

	for _, userID := range userIDs {
		var directCount int64
		var totalDownlineCount int64

		// 统计直接下级（depth=1）
		db.Model(&model.Referral{}).
			Where("referrer_id = ? AND depth = 1", userID).
			Count(&directCount)

		// 统计所有下级
		db.Model(&model.Referral{}).
			Where("referrer_id = ?", userID).
			Count(&totalDownlineCount)

		// upsert ReferralSnapshot
		var snapshot model.ReferralSnapshot
		err := db.Where("user_id = ?", userID).First(&snapshot).Error
		if err == gorm.ErrRecordNotFound {
			snapshot = model.ReferralSnapshot{
				UserID:             userID,
				DirectCount:        int(directCount),
				TotalDownlineCount: int(totalDownlineCount),
			}
			db.Create(&snapshot)
		} else if err == nil {
			db.Model(&snapshot).Updates(model.ReferralSnapshot{
				DirectCount:        int(directCount),
				TotalDownlineCount: int(totalDownlineCount),
			})
		} else {
			fmt.Printf("Error updating snapshot for user %s: %v\n", userID, err)
		}
	}
	fmt.Println("ReferralSnapshot Statistics completed")
}
