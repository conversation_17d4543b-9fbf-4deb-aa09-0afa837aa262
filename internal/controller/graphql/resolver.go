package graphql

import (
	"context"

	"github.com/google/uuid"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/config"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/repo"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/service"
)

// This file will not be regenerated automatically.
//
// It serves as dependency injection for your app, add any dependencies you require here.

type Resolver struct {
	UserRepo    repo.UserRepositoryInterface
	UserService service.UserServiceInterface
	JWTConfig   config.JWT
}

func NewResolver(jwtConfig config.JWT) *Resolver {
	return &Resolver{
		UserRepo:    repo.NewUserRepository(),
		UserService: service.NewUserService(),
		JWTConfig:   jwtConfig,
	}
}

func GetUserIDFromContext(ctx context.Context) uuid.UUID {
	userIdStr, _ := ctx.Value("userId").(string)
	userId := uuid.Nil
	if userIdStr != "" {
		userId, _ = uuid.Parse(userIdStr)
	}
	return userId
}
